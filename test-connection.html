<!DOCTYPE html>
<html>
<head>
    <title>Socket.io Connection Test</title>
</head>
<body>
    <h1>Socket.io Connection Test</h1>
    <div id="status">Testing connection...</div>
    <div id="log"></div>

    <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>
    <script>
        const log = document.getElementById('log');
        const status = document.getElementById('status');
        
        function addLog(message) {
            const div = document.createElement('div');
            div.textContent = new Date().toLocaleTimeString() + ': ' + message;
            log.appendChild(div);
            console.log(message);
        }
        
        addLog('Starting connection test...');
        
        const socket = io('http://localhost:4000', {
            transports: ['polling', 'websocket'],
            timeout: 20000,
            forceNew: true
        });
        
        socket.on('connect', () => {
            addLog('✅ Connected successfully! Socket ID: ' + socket.id);
            status.textContent = 'Connected!';
            status.style.color = 'green';
        });
        
        socket.on('connect_error', (error) => {
            addLog('❌ Connection error: ' + error.message);
            status.textContent = 'Connection failed!';
            status.style.color = 'red';
        });
        
        socket.on('disconnect', (reason) => {
            addLog('❌ Disconnected: ' + reason);
            status.textContent = 'Disconnected!';
            status.style.color = 'orange';
        });
        
        // Test creating a room after connection
        socket.on('connect', () => {
            setTimeout(() => {
                addLog('Testing room creation...');
                socket.emit('create-room', { playerName: 'TestUser' });
            }, 1000);
        });
        
        socket.on('room-created', (roomCode) => {
            addLog('✅ Room created successfully: ' + roomCode);
        });
        
        socket.on('game-state-update', (state) => {
            addLog('✅ Game state update received. Phase: ' + state.gamePhase);
        });
    </script>
</body>
</html>
