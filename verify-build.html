<!DOCTYPE html>
<html>
<head>
    <title>Verify Sammy UNO Paradise Build</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .check { color: green; } .error { color: red; }
        .info { background: #f0f0f0; padding: 10px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🔍 Sammy UNO Paradise - Build Verification</h1>
    
    <div class="info">
        <strong>Backend URL:</strong> https://swcf60xs-4000.inc1.devtunnels.ms<br>
        <strong>Build Status:</strong> <span id="buildStatus">Checking...</span>
    </div>

    <h2>Build Files Check</h2>
    <div id="fileChecks"></div>

    <h2>Backend Connection Test</h2>
    <div id="backendTest">Testing connection...</div>

    <script>
        const requiredFiles = [
            'dist/index.html',
            'dist/manifest.json',
            'dist/sw.js',
            'dist/favicon.svg',
            'dist/icon-192x192.png',
            'dist/icon-512x512.png'
        ];

        async function checkFiles() {
            const fileChecks = document.getElementById('fileChecks');
            let allGood = true;

            for (const file of requiredFiles) {
                try {
                    const response = await fetch('/' + file);
                    const status = response.ok ? '✅' : '❌';
                    const statusText = response.ok ? 'Found' : 'Missing';
                    fileChecks.innerHTML += `<div>${status} ${file} - ${statusText}</div>`;
                    if (!response.ok) allGood = false;
                } catch (error) {
                    fileChecks.innerHTML += `<div>❌ ${file} - Error checking</div>`;
                    allGood = false;
                }
            }

            document.getElementById('buildStatus').innerHTML = allGood ? 
                '<span class="check">✅ All files present</span>' : 
                '<span class="error">❌ Some files missing</span>';
        }

        async function testBackend() {
            const backendTest = document.getElementById('backendTest');
            try {
                const response = await fetch('https://swcf60xs-4000.inc1.devtunnels.ms');
                if (response.ok || response.status === 404) {
                    backendTest.innerHTML = '✅ Backend is accessible';
                } else {
                    backendTest.innerHTML = '❌ Backend returned error: ' + response.status;
                }
            } catch (error) {
                backendTest.innerHTML = '❌ Backend connection failed: ' + error.message;
            }
        }

        // Run checks
        checkFiles();
        testBackend();
    </script>

    <h2>Deployment Ready!</h2>
    <p>Your Sammy UNO Paradise is ready for deployment to Netlify:</p>
    <ol>
        <li>Go to <a href="https://netlify.com" target="_blank">netlify.com</a></li>
        <li>Drag the <code>dist</code> folder to deploy</li>
        <li>Set site name to <code>sammy-uno-paradise</code></li>
        <li>Enjoy your live UNO game!</li>
    </ol>
</body>
</html>
