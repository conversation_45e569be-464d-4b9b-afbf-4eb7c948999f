<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#18181b" />
    <link rel="manifest" href="/manifest.json" />
    <title>UNO Club</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
      /* Custom font to make it look nicer */
      @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&display=swap');
      html, body, #root {
        height: 100%;
      }
      body {
        font-family: 'Poppins', sans-serif;
      }
    </style>
  <script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "socket.io-client": "https://esm.sh/socket.io-client@4.7.5",
    "socket.io": "https://esm.sh/socket.io@^4.8.1",
    "express": "https://esm.sh/express@^5.1.0",
    "http": "https://esm.sh/http@^0.0.1-security",
    "cors": "https://esm.sh/cors@^2.8.5",
    "path": "https://esm.sh/path@^0.12.7"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body class="bg-zinc-900 text-white">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
</body>
</html>