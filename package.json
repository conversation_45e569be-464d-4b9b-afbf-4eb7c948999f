{"name": "uno-club", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "build:production": "vite build --mode production", "serve": "vite preview --port 4173"}, "dependencies": {"cors": "^2.8.5", "express": "^5.1.0", "http": "^0.0.1-security", "path": "^0.12.7", "react": "^19.1.0", "react-dom": "^19.1.0", "socket.io": "^4.8.1", "socket.io-client": "4.7.5"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^22.14.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "~5.7.2", "vite": "^6.2.0"}}