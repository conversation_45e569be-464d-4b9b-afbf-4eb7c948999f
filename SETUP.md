
# Local Development Setup Guide

Follow these instructions to set up and run the UNO Club application on your local machine. This project consists of a Node.js backend and a React frontend that is served by the backend.

## Prerequisites

- **Node.js**: Version 18.x or later.
- **npm**: Should be included with your Node.js installation.

## Installation

### 1. <PERSON><PERSON> the Repository

First, clone the project from GitHub to your local machine.

```bash
git clone <your-repository-url>
cd <repository-folder-name>
```

### 2. Install Backend Dependencies

The backend manages all game logic and dependencies. The frontend uses an `importmap` in `index.html` to load React and other libraries from a CDN, so it does not require a separate `npm install` step.

Navigate to the backend directory and install the required npm packages.

```bash
cd backend
npm install
```

## Running the Application

The backend server must be running to play the game, as it serves the frontend files and manages the real-time game state.

### 1. For Development (with Auto-Reload)

This is the recommended method for active development. It uses `nodemon` and `ts-node` to automatically restart the server whenever you make changes to the backend's TypeScript files.

From the `backend` directory, run:

```bash
npm run dev
```

The server will start, typically on port 4000.

### 2. For Production

This method first compiles the TypeScript code into JavaScript and then runs the compiled code. This is how you would run the application on a production server.

From the `backend` directory, run:

```bash
# Step 1: Build the TypeScript code (compiles src/*.ts to dist/*.js)
npm run build

# Step 2: Start the server
npm start
```

## Accessing the Game

Once the backend server is running (using either the `dev` or `start` command), you can access the application by opening your web browser and navigating to:

**[http://localhost:4000](http://localhost:4000)**

## Important Notes

### Service Worker and Caching

- The application uses a service worker (`public/sw.js`) to cache assets for PWA functionality and offline access.
- During development, your browser might aggressively cache old versions of files. If you don't see your changes reflected, **open your browser's DevTools**, go to the **Application** tab, find the **Service Workers** section, and click **"Unregister"**. Then, do a hard refresh (Ctrl+Shift+R or Cmd+Shift+R).

### CORS Configuration

- The backend's CORS policy is set to allow all origins (`"*"`) in `backend/src/server.ts` for development convenience.
- For a production deployment, you should restrict this to the specific domain where your frontend is hosted.
