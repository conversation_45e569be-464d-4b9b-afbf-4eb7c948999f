import express from 'express';
import http from 'http';
import { Server } from 'socket.io';
import cors from 'cors';
import { socketHandler } from './socketHandler';
import path from 'path';

const app = express();
app.use(cors());

const server = http.createServer(app);

const io = new Server(server, {
  cors: {
    origin: ["http://localhost:5173", "http://localhost:5174", "http://localhost:3000"], // Allow common dev ports
    methods: ["GET", "POST"],
    credentials: true
  }
});

// Serve static files from the project root (where index.html is located)
const staticPath = path.join(__dirname, '..', '..');
app.use(express.static(staticPath));

app.get('*', (req, res) => {
    res.sendFile(path.join(staticPath, 'index.html'));
});


io.on('connection', (socket) => {
  socketHandler(io, socket);
});

const PORT = process.env.PORT || 4000;
server.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});
