import { Server, Socket } from 'socket.io';
import { GamePhase, GameState, HouseRules, Player, Card, CardColor, CardValue } from './types';
import { createDeck, dealCards, getNextPlayerIndex, isCardPlayable } from './gameLogic';

const rooms: Record<string, GameState> = {};

const INITIAL_HOUSE_RULES: HouseRules = {
  stacking: true,
  jumpIn: false,
  sevenZero: false,
};

const createInitialGameState = (roomCode: string, host: Player): GameState => ({
  gamePhase: GamePhase.LOBBY,
  roomCode,
  players: [host],
  deck: [],
  discardPile: [],
  currentPlayerIndex: 0,
  playDirection: 'forward',
  isColorPickerOpen: false,
  playerPendingColorPick: null,
  winner: null,
  houseRules: INITIAL_HOUSE_RULES,
  message: null,
  drawTwoStack: 0,
  drawFourStack: 0,
});

const getRoomCodeFromSocket = (socket: Socket): string | undefined => {
    return Object.keys(rooms).find(rc => rooms[rc].players.some(p => p.id === socket.id));
}

export const socketHandler = (io: Server, socket: Socket) => {
    console.log(`User connected: ${socket.id}`);

    const emitGameState = (roomCode: string) => {
        if (rooms[roomCode]) {
            // Clear message after a short delay
            if (rooms[roomCode].message) {
                setTimeout(() => {
                    if (rooms[roomCode]) {
                       rooms[roomCode].message = null;
                       io.to(roomCode).emit('game-state-update', rooms[roomCode]);
                    }
                }, 2000);
            }
            io.to(roomCode).emit('game-state-update', rooms[roomCode]);
        }
    };
    
    const leaveRoom = () => {
        const roomCode = getRoomCodeFromSocket(socket);
        if (roomCode && rooms[roomCode]) {
            rooms[roomCode].players = rooms[roomCode].players.filter(p => p.id !== socket.id);
            if (rooms[roomCode].players.length === 0) {
                delete rooms[roomCode];
                console.log(`Room ${roomCode} closed.`);
            } else {
                if (rooms[roomCode].gamePhase === GamePhase.GAME && rooms[roomCode].players.length < 2) {
                     rooms[roomCode].winner = rooms[roomCode].players[0];
                     rooms[roomCode].gamePhase = GamePhase.GAME_OVER;
                }
                if (!rooms[roomCode].players.some(p => p.isHost)) {
                    rooms[roomCode].players[0].isHost = true;
                }
                emitGameState(roomCode);
            }
        }
    };

    socket.on('create-room', ({ playerName }: { playerName: string }) => {
        const roomCode = Math.random().toString(36).substring(2, 7).toUpperCase();
        const host: Player = { id: socket.id, name: playerName, hand: [], isHost: true, saidUno: false };
        rooms[roomCode] = createInitialGameState(roomCode, host);
        socket.join(roomCode);
        socket.emit('room-created', roomCode);
        emitGameState(roomCode);
    });

    socket.on('join-room', ({ roomCode, playerName }: { roomCode: string, playerName: string }) => {
        if (rooms[roomCode] && rooms[roomCode].gamePhase === GamePhase.LOBBY) {
            const player: Player = { id: socket.id, name: playerName, hand: [], isHost: false, saidUno: false };
            rooms[roomCode].players.push(player);
            socket.join(roomCode);
            emitGameState(roomCode);
        } else {
            socket.emit('error-message', 'Room not found or game already in progress.');
        }
    });
    
    socket.on('set-house-rule', ({ roomCode, rule, value }: { roomCode: string, rule: keyof HouseRules, value: boolean }) => {
        if (rooms[roomCode] && rooms[roomCode].players.find(p => p.id === socket.id)?.isHost) {
            rooms[roomCode].houseRules[rule] = value;
            emitGameState(roomCode);
        }
    });

    socket.on('start-game', ({ roomCode }: { roomCode: string }) => {
        const room = rooms[roomCode];
        if (room && room.players.find(p => p.id === socket.id)?.isHost && room.players.length >= 2) {
            room.gamePhase = GamePhase.GAME;
            room.deck = createDeck();
            const dealt = dealCards(room.players, room.deck);
            room.players = dealt.players;
            room.deck = dealt.deck;
            
            let firstCard = room.deck.shift()!;
            while(firstCard.value === CardValue.WILD_DRAW_FOUR) {
                room.deck.push(firstCard);
                firstCard = room.deck.shift()!;
            }
            room.discardPile = [firstCard];
            
            if (firstCard.color === CardColor.BLACK) {
                room.playerPendingColorPick = room.players[0].id;
                room.isColorPickerOpen = true;
            } else {
                if(firstCard.value === CardValue.SKIP) {
                    room.currentPlayerIndex = getNextPlayerIndex(room, 1);
                }
                if(firstCard.value === CardValue.REVERSE) {
                    room.playDirection = 'backward';
                }
                if(firstCard.value === CardValue.DRAW_TWO) {
                    room.drawTwoStack = 2;
                }
            }
            emitGameState(roomCode);
        }
    });

    socket.on('play-card', ({ roomCode, cardId }: { roomCode: string, cardId: string }) => {
        const room = rooms[roomCode];
        const player = room?.players[room.currentPlayerIndex];
        if (!room || !player || player.id !== socket.id) return;
        
        const card = player.hand.find(c => c.id === cardId);
        if (!card) return;
        
        if (!isCardPlayable(card, room.discardPile[0], room.houseRules, room.drawTwoStack, room.drawFourStack)) {
            socket.emit('error-message', "You can't play that card!");
            return;
        }

        player.hand = player.hand.filter(c => c.id !== cardId);
        room.discardPile.unshift(card);

        if (player.hand.length === 1 && !player.saidUno) {
            room.message = `${player.name} forgot to call UNO! Drawing 2 cards.`;
            player.hand.push(...room.deck.splice(0, 2));
        }
        player.saidUno = false; // Reset on every play

        if (player.hand.length === 0) {
            room.gamePhase = GamePhase.GAME_OVER;
            room.winner = player;
            emitGameState(roomCode);
            return;
        }

        if (card.color === CardColor.BLACK) {
            room.isColorPickerOpen = true;
            room.playerPendingColorPick = player.id;
            if (card.value === CardValue.WILD_DRAW_FOUR) {
                room.drawFourStack += 4;
            }
            emitGameState(roomCode); // Wait for color pick
            return;
        }
        
        let nextPlayerStep = 1;
        if(card.value === CardValue.REVERSE) {
            room.playDirection = room.playDirection === 'forward' ? 'backward' : 'forward';
        }
        if(card.value === CardValue.SKIP) {
            nextPlayerStep = 2;
        }
        if(card.value === CardValue.DRAW_TWO) {
            room.drawTwoStack += 2;
        }
        
        room.currentPlayerIndex = getNextPlayerIndex(room, nextPlayerStep);
        emitGameState(roomCode);
    });
    
    socket.on('draw-card', ({ roomCode }: { roomCode: string }) => {
        const room = rooms[roomCode];
        const player = room?.players[room.currentPlayerIndex];
        if (!room || !player || player.id !== socket.id) return;
        
        if (room.drawFourStack > 0) {
            player.hand.push(...room.deck.splice(0, room.drawFourStack));
            room.message = `${player.name} drew ${room.drawFourStack} cards.`;
            room.drawFourStack = 0;
        } else if (room.drawTwoStack > 0) {
            player.hand.push(...room.deck.splice(0, room.drawTwoStack));
            room.message = `${player.name} drew ${room.drawTwoStack} cards.`;
            room.drawTwoStack = 0;
        } else {
            if (room.deck.length > 0) {
                player.hand.push(room.deck.shift()!);
            } else {
                room.message = "Deck is empty!";
            }
        }

        room.currentPlayerIndex = getNextPlayerIndex(room, 1);
        emitGameState(roomCode);
    });

    socket.on('set-wild-color', ({ roomCode, color }: { roomCode: string, color: CardColor }) => {
        const room = rooms[roomCode];
        if (!room || room.playerPendingColorPick !== socket.id) return;
        
        room.discardPile[0].color = color;
        room.isColorPickerOpen = false;
        room.playerPendingColorPick = null;
        
        let nextPlayerStep = 1;
        if (room.discardPile[0].value === CardValue.WILD_DRAW_FOUR) {
            // The penalty is applied when the next player tries to draw
            nextPlayerStep = 1; // Just move to the next player, they will be forced to draw
        }

        room.currentPlayerIndex = getNextPlayerIndex(room, nextPlayerStep);
        emitGameState(roomCode);
    });
    
    socket.on('say-uno', ({ roomCode }: { roomCode: string }) => {
        const room = rooms[roomCode];
        if (!room) return;
        const player = room.players.find(p => p.id === socket.id);
        if (player && player.hand.length === 2) {
            player.saidUno = true;
            room.message = `${player.name} called UNO!`;
            emitGameState(roomCode);
        }
    });

    socket.on('play-again', ({ roomCode }: { roomCode: string }) => {
        const room = rooms[roomCode];
        if(room && room.players.find(p => p.id === socket.id)?.isHost) {
            room.players.forEach(p => {
                p.hand = [];
                p.saidUno = false;
            });
            
            room.gamePhase = GamePhase.LOBBY;
            room.deck = [];
            room.discardPile = [];
            room.currentPlayerIndex = 0;
            room.playDirection = 'forward';
            room.isColorPickerOpen = false;
            room.playerPendingColorPick = null;
            room.winner = null;
            room.message = 'Host started a new game!';
            room.drawTwoStack = 0;
            room.drawFourStack = 0;

            emitGameState(roomCode);
        }
    });

    socket.on('disconnect', () => {
        console.log(`User disconnected: ${socket.id}`);
        leaveRoom();
    });
};