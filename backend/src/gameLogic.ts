import { Card, CardColor, CardValue, Player, GameState } from './types';

export const createDeck = (): Card[] => {
    const colors = [CardColor.RED, CardColor.YELLOW, CardColor.GREEN, CardColor.BLUE];
    const values: CardValue[] = [
        CardValue.ZERO, CardValue.ONE, CardValue.TWO, CardValue.THREE, CardValue.FOUR,
        CardValue.FIVE, CardValue.SIX, CardValue.SEVEN, CardValue.EIGHT, CardValue.NINE,
        CardValue.SKIP, CardValue.REVERSE, CardValue.DRAW_TWO
    ];
    let deck: Card[] = [];
    let idCounter = 0;

    colors.forEach(color => {
        // One of each number card
        deck.push({ id: `card-${idCounter++}`, color, value: CardValue.ZERO });
        // Two of each other value card
        for (let i = 1; i < values.length; i++) {
            deck.push({ id: `card-${idCounter++}`, color, value: values[i] });
            deck.push({ id: `card-${idCounter++}`, color, value: values[i] });
        }
    });

    for (let i = 0; i < 4; i++) {
        deck.push({ id: `card-${idCounter++}`, color: CardColor.BLACK, value: CardValue.WILD });
        deck.push({ id: `card-${idCounter++}`, color: CardColor.BLACK, value: CardValue.WILD_DRAW_FOUR });
    }
    
    // Shuffle
    for (let i = deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [deck[i], deck[j]] = [deck[j], deck[i]];
    }

    return deck;
};

export const dealCards = (players: Player[], deck: Card[]): { players: Player[], deck: Card[] } => {
    const newPlayers = players.map(player => ({
        ...player,
        hand: deck.splice(0, 7)
    }));
    return { players: newPlayers, deck };
};

export const isCardPlayable = (card: Card, topCard: Card, houseRules: GameState['houseRules'], drawTwoStack: number, drawFourStack: number): boolean => {
    // If a draw-four stack is active, stacking is the only way out.
    if (drawFourStack > 0) {
        return houseRules.stacking && card.value === CardValue.WILD_DRAW_FOUR;
    }
    // If a draw-two stack is active, stacking is the only way out.
    if (drawTwoStack > 0) {
        return houseRules.stacking && card.value === CardValue.DRAW_TWO;
    }

    // No active draw stacks, standard rules apply.
    return card.color === CardColor.BLACK || card.color === topCard.color || card.value === topCard.value;
};

export const getNextPlayerIndex = (state: GameState, step: number = 1): number => {
    const { currentPlayerIndex, players, playDirection } = state;
    const numPlayers = players.length;
    const directionStep = playDirection === 'forward' ? 1 : -1;
    return (currentPlayerIndex + (directionStep * step) + numPlayers) % numPlayers;
};