import React from 'react';
import { GameProvider, useGame } from './context/GameContext';
import { GamePhase } from './types';
import HomeScreen from './screens/HomeScreen';
import LobbyScreen from './screens/LobbyScreen';
import GameScreen from './screens/GameScreen';
import GameOverScreen from './screens/GameOverScreen';

const AppContent: React.FC = () => {
  const { state } = useGame();

  if (state.gamePhase === GamePhase.CONNECTING) {
    return <div className="min-h-screen flex items-center justify-center bg-zinc-900 text-white">Connecting...</div>;
  }

  const renderScreen = () => {
    switch (state.gamePhase) {
      case GamePhase.HOME:
        return <HomeScreen />;
      case GamePhase.LOBBY:
        return <LobbyScreen />;
      case GamePhase.GAME:
        return <GameScreen />;
      case GamePhase.GAME_OVER:
        return <GameOverScreen />;
      default:
        return <HomeScreen />;
    }
  };

  return <div className="antialiased">{renderScreen()}</div>;
};

const App: React.FC = () => {
  return (
    <GameProvider>
      <AppContent />
    </GameProvider>
  );
};

export default App;
