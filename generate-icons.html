<!DOCTYPE html>
<html>
<head>
    <title>Generate UNO Club Icons</title>
</head>
<body>
    <h2>UNO Club Icon Generator</h2>
    <canvas id="canvas192" width="192" height="192" style="border: 1px solid #ccc; margin: 10px;"></canvas>
    <canvas id="canvas512" width="512" height="512" style="border: 1px solid #ccc; margin: 10px;"></canvas>
    <br>
    <button onclick="generateIcons()">Generate Icons</button>
    <div id="output"></div>

    <script>
        function createIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            
            // Background (dark zinc)
            ctx.fillStyle = '#18181b';
            ctx.fillRect(0, 0, size, size);
            
            // Main red background with rounded corners
            const padding = size * 0.1;
            const radius = size * 0.15;
            ctx.fillStyle = '#dc2626';
            ctx.beginPath();
            ctx.roundRect(padding, padding, size - 2*padding, size - 2*padding, radius);
            ctx.fill();
            
            // UNO text
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.25}px Arial, sans-serif`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.strokeStyle = 'white';
            ctx.lineWidth = size * 0.01;
            ctx.strokeText('UNO', size * 0.5, size * 0.4);
            ctx.fillText('UNO', size * 0.5, size * 0.4);
            
            // CLUB text
            ctx.font = `bold ${size * 0.12}px Arial, sans-serif`;
            ctx.fillText('CLUB', size * 0.5, size * 0.65);
            
            // Decorative cards at bottom
            const cardWidth = size * 0.15;
            const cardHeight = size * 0.2;
            const cardY = size * 0.75;
            
            // Left card (Blue)
            ctx.fillStyle = '#3b82f6';
            ctx.beginPath();
            ctx.roundRect(size * 0.2, cardY, cardWidth, cardHeight, size * 0.02);
            ctx.fill();
            
            // Right card (Yellow)
            ctx.fillStyle = '#eab308';
            ctx.beginPath();
            ctx.roundRect(size * 0.65, cardY, cardWidth, cardHeight, size * 0.02);
            ctx.fill();
            
            // Card numbers
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.08}px Arial, sans-serif`;
            ctx.fillText('7', size * 0.275, cardY + cardHeight * 0.5);
            ctx.fillText('9', size * 0.725, cardY + cardHeight * 0.5);
        }

        function generateIcons() {
            const canvas192 = document.getElementById('canvas192');
            const canvas512 = document.getElementById('canvas512');
            
            createIcon(canvas192, 192);
            createIcon(canvas512, 512);
            
            // Convert to base64 and show download links
            const data192 = canvas192.toDataURL('image/png');
            const data512 = canvas512.toDataURL('image/png');
            
            const output = document.getElementById('output');
            output.innerHTML = `
                <h3>Generated Icons:</h3>
                <p><a href="${data192}" download="icon-192x192.png">Download 192x192 Icon</a></p>
                <p><a href="${data512}" download="icon-512x512.png">Download 512x512 Icon</a></p>
                <h4>Base64 Data (for manual creation):</h4>
                <p><strong>192x192:</strong></p>
                <textarea style="width: 100%; height: 100px;">${data192}</textarea>
                <p><strong>512x512:</strong></p>
                <textarea style="width: 100%; height: 100px;">${data512}</textarea>
            `;
        }
        
        // Auto-generate on load
        window.onload = generateIcons;
    </script>
</body>
</html>
