# Sammy UNO Paradise - Deployment Guide

## Overview
This is a full-stack UNO game application with:
- **Frontend**: React + TypeScript + Vite (deployed on Netlify)
- **Backend**: Node.js + Express + Socket.io (deployed on Render)

## Deployment Steps

### 1. Backend Deployment (Render)

1. **Create a new Web Service on Render**:
   - Go to [render.com](https://render.com)
   - Connect your GitHub repository
   - Select the `backend` folder as the root directory
   - Use these settings:
     - **Name**: `sammy-uno-paradise-backend`
     - **Environment**: `Node`
     - **Build Command**: `npm install && npm run build`
     - **Start Command**: `npm start`
     - **Plan**: Free

2. **Set Environment Variables**:
   ```
   NODE_ENV=production
   PORT=10000
   CORS_ORIGIN=https://sammy-uno-paradise.netlify.app
   ```

3. **Deploy**: The backend will be available at:
   `https://sammy-uno-paradise-backend.onrender.com`

### 2. Frontend Deployment (Netlify)

1. **Connect Repository to Netlify**:
   - Go to [netlify.com](https://netlify.com)
   - Click "New site from Git"
   - Connect your GitHub repository
   - Use these settings:
     - **Site name**: `sammy-uno-paradise`
     - **Build command**: `npm run build`
     - **Publish directory**: `dist`

2. **Environment Variables** (set in Netlify dashboard):
   ```
   VITE_BACKEND_URL=https://sammy-uno-paradise-backend.onrender.com
   VITE_APP_NAME=Sammy UNO Paradise
   VITE_APP_VERSION=1.0.0
   ```

3. **Deploy**: The frontend will be available at:
   `https://sammy-uno-paradise.netlify.app`

## Local Development

### Frontend
```bash
npm install
npm run dev
```

### Backend
```bash
cd backend
npm install
npm run dev
```

## Production Features

✅ **Progressive Web App (PWA)**
- Installable on mobile devices
- Offline caching with Service Worker
- App manifest for native-like experience

✅ **Real-time Multiplayer**
- Socket.io for instant game updates
- Room-based gameplay
- Automatic reconnection

✅ **Production Optimizations**
- Tailwind CSS build optimization
- Environment-based configuration
- Security headers
- Asset caching

✅ **Game Features**
- Complete UNO game logic
- House rules (card stacking, etc.)
- UNO call system
- Win detection
- Play again functionality

## Architecture

```
Frontend (Netlify)     Backend (Render)
     ↓                      ↓
React App              Node.js Server
   ↓                      ↓
Socket.io Client  ←→  Socket.io Server
   ↓                      ↓
Game UI               Game Logic
```

## Support

For issues or questions, check the console logs in your browser's developer tools.
