import { GamePhase, CardColor, CardValue, GameState, HouseRules } from './types';
import React from 'react';

export const INITIAL_HOUSE_RULES: HouseRules = {
  stacking: true,
  jumpIn: false,
  sevenZero: false,
};

export const INITIAL_GAME_STATE: GameState = {
  gamePhase: GamePhase.CONNECTING,
  roomCode: null,
  players: [],
  deck: [],
  discardPile: [],
  currentPlayerIndex: 0,
  playDirection: 'forward',
  isColorPickerOpen: false,
  playerPendingColorPick: null,
  winner: null,
  houseRules: INITIAL_HOUSE_RULES,
  message: null,
  drawTwoStack: 0,
  drawFourStack: 0,
};

export const SkipIcon: React.FC<{className?: string}> = ({ className }) => (
    <svg className={className} viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2zm0 3a7 7 0 00-7 7 7 7 0 007 7 7 7 0 007-7 7 7 0 00-7-7zm-1.707 9.707l-1.414-1.414L10.586 12 8.879 10.293l1.414-1.414L12 10.586l1.707-1.707 1.414 1.414L13.414 12l1.707 1.707-1.414 1.414L12 13.414l-1.707 1.707z"></path>
    </svg>
);

export const ReverseIcon: React.FC<{className?: string}> = ({ className }) => (
    <svg className={className} viewBox="0 0 24 24" fill="currentColor">
        <path d="M6 9l4-4v3h2.172a4.001 4.001 0 013.163 1.525l.698 1.046a5.001 5.001 0 01-1.33 7.02l-.683.569A5.996 5.996 0 0018 19.828V20h-2v-1.172a4 4 0 01-3.163-1.525l-.698-1.046a5.001 5.001 0 011.33-7.02l.683-.569A5.996 5.996 0 006 9.828V9zM18 15l-4 4v-3h-2.172a4.001 4.001 0 01-3.163-1.525l-.698-1.046a5.001 5.001 0 011.33-7.02l.683-.569A5.996 5.996 0 006 4.172V4h2v1.172a4 4 0 013.163 1.525l.698 1.046a5.001 5.001 0 01-1.33 7.02l-.683.569A5.996 5.996 0 0018 14.828V15z"></path>
    </svg>
);

export const DrawTwoIcon: React.FC<{className?: string}> = ({ className }) => (
    <div className={`${className} flex items-center justify-center font-bold text-xl`}>+2</div>
);

export const DrawFourIcon: React.FC<{className?: string}> = ({ className }) => (
    <div className={`${className} flex items-center justify-center font-bold text-xl`}>+4</div>
);

export const CARD_COLOR_MAP: { [key in CardColor]: string } = {
  [CardColor.RED]: 'bg-red-600',
  [CardColor.YELLOW]: 'bg-yellow-400',
  [CardColor.GREEN]: 'bg-green-500',
  [CardColor.BLUE]: 'bg-blue-500',
  [CardColor.BLACK]: 'bg-zinc-800',
};

export const CARD_TEXT_COLOR_MAP: { [key in CardColor]: string } = {
    [CardColor.RED]: 'text-white',
    [CardColor.YELLOW]: 'text-zinc-800',
    [CardColor.GREEN]: 'text-white',
    [CardColor.BLUE]: 'text-white',
    [CardColor.BLACK]: 'text-white',
};