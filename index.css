/* UNO Club - Additional Styles */

/* Ensure full height layout */
html, body, #root {
  height: 100%;
  margin: 0;
  padding: 0;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #27272a;
}

::-webkit-scrollbar-thumb {
  background: #52525b;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #71717a;
}

/* Card hover animations */
.card-hover {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Button press animation */
.btn-press {
  transition: transform 0.1s ease;
}

.btn-press:active {
  transform: scale(0.98);
}

/* Pulse animation for current player indicator */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.pulse {
  animation: pulse 2s infinite;
}

/* Fade in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Card flip animation */
@keyframes cardFlip {
  0% {
    transform: rotateY(0deg);
  }
  50% {
    transform: rotateY(90deg);
  }
  100% {
    transform: rotateY(0deg);
  }
}

.card-flip {
  animation: cardFlip 0.6s ease-in-out;
}

/* Shake animation for invalid moves */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

.shake {
  animation: shake 0.5s ease-in-out;
}

/* Glow effect for playable cards */
.card-glow {
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.5);
  border: 2px solid #22c55e;
}

/* UNO button special styling */
.uno-button {
  background: linear-gradient(45deg, #dc2626, #ef4444);
  box-shadow: 0 4px 15px rgba(220, 38, 38, 0.4);
  transform: scale(1);
  transition: all 0.2s ease;
}

.uno-button:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(220, 38, 38, 0.6);
}

.uno-button:active {
  transform: scale(0.95);
}

/* Loading spinner */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.spinner {
  animation: spin 1s linear infinite;
}

/* Toast notification styles */
.toast {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  padding: 12px 20px;
  background: #1f2937;
  color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.toast.show {
  transform: translateX(0);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .card-hover:hover {
    transform: none;
  }
  
  /* Larger touch targets on mobile */
  button {
    min-height: 44px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card-glow {
    border-width: 3px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles for accessibility */
button:focus-visible,
input:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Custom focus ring for cards */
.card:focus-visible {
  outline: 3px solid #22c55e;
  outline-offset: 3px;
}
