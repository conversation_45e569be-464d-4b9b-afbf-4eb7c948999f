
# UNO Club - The Definitive PWA Experience

![UNO Club Gameplay](https://i.imgur.com/qE4J3gX.png)

**UNO Club** is a fast, modern, and socially-driven online UNO Progressive Web App (PWA). It allows friends and couples to instantly create private game rooms and enjoy the classic card game with a slick, responsive interface and optional, chaotic "House Rules."

The core focus is a seamless user experience, from generating a room code to shouting "UNO!" on the final card. The application feels native, with instant loading times after the first visit, offline accessibility for the app shell, and a design that is as intuitive on a mobile phone as it is on a desktop.

## ✨ Features

- **Real-Time Multiplayer**: Gameplay is synchronized across all clients using a Node.js and Socket.io backend.
- **Private Game Rooms**: Instantly create a room and share the unique 5-character code with friends.
- **Customizable House Rules**: The host can enable popular rule variations like **Stacking** (+2 and +4 cards).
- **Classic UNO Gameplay**: Full implementation of <PERSON><PERSON>, Reverse, Draw Two, Wild, and Wild Draw Four cards.
- **"UNO!" Button Mechanic**: Forget to press the "UNO!" button with two cards left, and you'll be penalized with two more!
- **Progressive Web App (PWA)**: Installable on any device (desktop or mobile) for an app-like experience. Core assets are cached for fast loading and offline access.
- **Fully Responsive Design**: A vertical layout for mobile and a wider layout for desktop ensure a great experience on any screen size.
- **Clean, Modern UI**: Built with Tailwind CSS for a professional and intuitive look and feel.

## 🚀 Tech Stack

### Frontend
- **Framework**: React 19
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Real-time Communication**: Socket.io-client
- **PWA**: Implemented with a custom Service Worker and Web App Manifest.
- **Module Loading**: Uses modern `importmap` in `index.html` to load dependencies from a CDN, eliminating the need for a local bundler like Webpack or Vite for the frontend.

### Backend
- **Platform**: Node.js
- **Framework**: Express.js
- **Language**: TypeScript
- **Real-time Communication**: Socket.io
- **Build**: `tsc` (TypeScript Compiler)

## 🕹️ How to Play

1.  Open the web app on your browser.
2.  Enter a nickname for yourself.
3.  Click **"Create Room"** to get a unique room code to share, or click **"Join Room"** and enter a code from a friend.
4.  Once in the lobby, the host can toggle House Rules and press **"Start Game"** when at least two players have joined.
5.  Play your cards by matching color or value. Use action cards to mix up the game. Be the first to get rid of all your cards to win!

## 📂 Project Structure

```
/
├── backend/
│   ├── src/
│   │   ├── gameLogic.ts      # Core UNO rules (deck creation, card playability)
│   │   ├── socketHandler.ts  # Manages all real-time game events
│   │   ├── server.ts         # Express & Socket.io server setup
│   │   └── types.ts          # Shared types for the backend
│   ├── package.json
│   └── tsconfig.json
├── public/
│   ├── manifest.json         # PWA configuration
│   └── sw.js                 # Service Worker for caching and offline support
├── src/
│   ├── components/           # Reusable React components (Card, Modal)
│   ├── context/              # Global state management (GameContext)
│   ├── screens/              # Top-level page components (Home, Lobby, Game)
│   ├── App.tsx
│   ├── index.tsx
│   └── types.ts              # Shared types for the frontend
├── index.html                # Main entry point with importmap
└── README.md
```
