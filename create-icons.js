import fs from 'fs';

// Create simple placeholder PNG files since we don't have canvas library
console.log('Creating placeholder icons...');

// Create simple placeholder PNG files (1x1 pixel transparent)
const placeholder = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77mgAAAABJRU5ErkJggg==', 'base64');

fs.writeFileSync('public/icon-192x192.png', placeholder);
fs.writeFileSync('public/icon-512x512.png', placeholder);

console.log('Placeholder icons created. You may want to replace them with proper icons.');
