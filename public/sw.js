const CACHE_NAME = 'uno-club-v2';

// App shell files and essential assets
const CORE_ASSETS = [
  '/',
  '/index.html',
  '/index.css',
  '/manifest.json',
  '/favicon.svg'
  // Note: CDN resources are not cached to avoid CORS issues
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Opened cache, caching core assets');
        return cache.addAll(CORE_ASSETS);
      })
      .catch(err => {
        console.error('Failed to cache core assets:', err);
      })
  );
  self.skipWaiting();
});

self.addEventListener('fetch', (event) => {
  // We only want to cache GET requests.
  if (event.request.method !== 'GET') {
    return;
  }

  event.respondWith(
    caches.open(CACHE_NAME).then(async (cache) => {
      const cachedResponse = await cache.match(event.request);
      
      const fetchedResponse = fetch(event.request).then((networkResponse) => {
        // We don't cache requests to the backend API (socket.io polling)
        if (networkResponse.url.includes('/socket.io/')) {
          return networkResponse;
        }

        if(networkResponse && networkResponse.ok) {
            cache.put(event.request, networkResponse.clone());
        }
        return networkResponse;
      }).catch(() => {
        // Network failed, we're offline
        console.log('Fetch failed, returning cached response if available.');
      });

      return cachedResponse || fetchedResponse;
    })
  );
});


self.addEventListener('activate', (event) => {
  const cacheWhitelist = [CACHE_NAME];
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheWhitelist.indexOf(cacheName) === -1) {
            console.log('Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
  return self.clients.claim();
});