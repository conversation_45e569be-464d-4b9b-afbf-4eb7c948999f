<!DOCTYPE html>
<html>
<head>
    <title>UNO Club Icon Generator</title>
</head>
<body>
    <canvas id="canvas192" width="192" height="192" style="border: 1px solid #ccc; margin: 10px;"></canvas>
    <canvas id="canvas512" width="512" height="512" style="border: 1px solid #ccc; margin: 10px;"></canvas>
    <br>
    <button onclick="downloadIcons()">Download Icons</button>

    <script>
        function createIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            
            // Background
            ctx.fillStyle = '#18181b'; // zinc-900
            ctx.fillRect(0, 0, size, size);
            
            // Create rounded rectangle background
            const radius = size * 0.15;
            ctx.fillStyle = '#dc2626'; // red-600
            ctx.beginPath();
            ctx.roundRect(size * 0.1, size * 0.1, size * 0.8, size * 0.8, radius);
            ctx.fill();
            
            // UNO text
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.25}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('UNO', size * 0.5, size * 0.4);
            
            // Club text
            ctx.font = `bold ${size * 0.12}px Arial`;
            ctx.fillText('CLUB', size * 0.5, size * 0.65);
            
            // Card decoration
            const cardWidth = size * 0.15;
            const cardHeight = size * 0.2;
            
            // Left card
            ctx.fillStyle = '#3b82f6'; // blue-500
            ctx.beginPath();
            ctx.roundRect(size * 0.2, size * 0.75, cardWidth, cardHeight, size * 0.02);
            ctx.fill();
            
            // Right card
            ctx.fillStyle = '#eab308'; // yellow-500
            ctx.beginPath();
            ctx.roundRect(size * 0.65, size * 0.75, cardWidth, cardHeight, size * 0.02);
            ctx.fill();
        }

        // Create both icons
        createIcon(document.getElementById('canvas192'), 192);
        createIcon(document.getElementById('canvas512'), 512);

        function downloadIcons() {
            // Download 192x192 icon
            const canvas192 = document.getElementById('canvas192');
            const link192 = document.createElement('a');
            link192.download = 'icon-192x192.png';
            link192.href = canvas192.toDataURL();
            link192.click();
            
            // Download 512x512 icon
            setTimeout(() => {
                const canvas512 = document.getElementById('canvas512');
                const link512 = document.createElement('a');
                link512.download = 'icon-512x512.png';
                link512.href = canvas512.toDataURL();
                link512.click();
            }, 100);
        }
    </script>
</body>
</html>
