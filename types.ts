export enum GamePhase {
  CONNECTING = 'CONNECTING',
  HOME = 'HOME',
  LOBBY = 'LOBBY',
  GAME = 'GAME',
  GAME_OVER = 'GAME_OVER',
}

export enum CardColor {
  RED = 'RED',
  YELLOW = 'YELLOW',
  GREEN = 'GREEN',
  BLUE = 'BLUE',
  BLACK = 'BLACK',
}

export enum CardValue {
  ZERO = '0',
  ONE = '1',
  TWO = '2',
  THREE = '3',
  FOUR = '4',
  FIVE = '5',
  SIX = '6',
  SEVEN = '7',
  EIGHT = '8',
  NINE = '9',
  SKIP = 'SKIP',
  REVERSE = 'REVERSE',
  DRAW_TWO = 'DRAW_TWO',
  WILD = 'WILD',
  WILD_DRAW_FOUR = 'WILD_DRAW_FOUR',
}

export interface Card {
  id: string;
  color: CardColor;
  value: CardValue;
}

export interface Player {
  id: string; // Socket ID
  name: string;
  hand: Card[];
  isHost: boolean;
  saidUno: boolean;
}

export interface HouseRules {
  stacking: boolean;
  jumpIn: boolean;
  sevenZero: boolean;
}

export interface GameState {
  gamePhase: GamePhase;
  roomCode: string | null;
  players: Player[];
  deck: Card[];
  discardPile: Card[];
  currentPlayerIndex: number;
  playDirection: 'forward' | 'backward';
  isColorPickerOpen: boolean;
  playerPendingColorPick: string | null;
  winner: Player | null;
  houseRules: HouseRules;
  message: string | null;
  drawTwoStack: number;
  drawFourStack: number;
}

// Client-side state update action
export type GameAction = { type: 'SET_GAME_STATE'; payload: Partial<GameState> };