# 🃏 Sammy UNO Paradise

**The definitive online UNO experience** - A production-ready Progressive Web App (PWA) for real-time multiplayer UNO gaming.

## 🚀 Live Demo

- **Frontend**: [https://sammy-uno-paradise.netlify.app](https://sammy-uno-paradise.netlify.app)
- **Backend**: [https://sammy-uno-paradise-backend.onrender.com](https://sammy-uno-paradise-backend.onrender.com)

## ✨ Features

### 🎮 Complete UNO Experience
- **Real-time Multiplayer**: Instant game updates via Socket.io
- **Room-based Gameplay**: Create private rooms with 5-character codes
- **House Rules**: Enable card stacking and other variants
- **UNO Call System**: Don't forget to call UNO!
- **Win Detection**: Automatic game completion and scoring

### 📱 Progressive Web App
- **Installable**: Add to home screen on mobile devices
- **Offline Support**: Service Worker for caching
- **Responsive Design**: Perfect on desktop and mobile
- **Native Feel**: App-like experience

### 🔧 Production Ready
- **TypeScript**: Full type safety
- **Environment Configuration**: Development and production modes
- **Security Headers**: CSP, XSS protection, and more
- **Performance Optimized**: Asset caching and compression

## 🎯 How to Play

1. **Enter Your Nickname**: Start by choosing your player name
2. **Create or Join Room**: 
   - **Create**: Generate a 5-character room code to share
   - **Join**: Enter a friend's room code
3. **Lobby**: Wait for players and configure house rules (host only)
4. **Game**: Play cards, draw when needed, and don't forget UNO!
5. **Victory**: First to empty their hand wins!

## 🛠️ Tech Stack

### Frontend
- **React 19** + **TypeScript**
- **Vite** for build tooling
- **Tailwind CSS** for styling
- **Socket.io Client** for real-time communication
- **PWA** with Service Worker

### Backend
- **Node.js** + **Express**
- **TypeScript** for type safety
- **Socket.io** for real-time multiplayer
- **CORS** configured for production

## 🚀 Deployment Instructions

### 1. Backend Deployment (Render)

1. **Create a new Web Service** on [render.com](https://render.com)
2. **Connect your GitHub repository**
3. **Configure the service**:
   - **Name**: `sammy-uno-paradise-backend`
   - **Root Directory**: `backend`
   - **Environment**: `Node`
   - **Build Command**: `npm install && npm run build`
   - **Start Command**: `npm start`
   - **Plan**: Free

4. **Set Environment Variables**:
   ```
   NODE_ENV=production
   PORT=10000
   CORS_ORIGIN=https://sammy-uno-paradise.netlify.app
   ```

5. **Deploy**: Your backend will be available at:
   `https://sammy-uno-paradise-backend.onrender.com`

### 2. Frontend Deployment (Netlify)

1. **Connect Repository** to [netlify.com](https://netlify.com)
2. **Configure the site**:
   - **Site name**: `sammy-uno-paradise`
   - **Build command**: `npm run build`
   - **Publish directory**: `dist`

3. **Set Environment Variables** in Netlify dashboard:
   ```
   VITE_BACKEND_URL=https://sammy-uno-paradise-backend.onrender.com
   VITE_APP_NAME=Sammy UNO Paradise
   VITE_APP_VERSION=1.0.0
   ```

4. **Deploy**: Your frontend will be available at:
   `https://sammy-uno-paradise.netlify.app`

## 💻 Local Development

### Prerequisites
- Node.js 18+
- npm

### Setup

1. **Clone and install**:
   ```bash
   git clone <your-repo-url>
   cd uno-club
   npm install
   cd backend && npm install
   ```

2. **Start development servers**:
   ```bash
   # Terminal 1: Backend
   cd backend && npm run dev
   
   # Terminal 2: Frontend
   npm run dev
   ```

3. **Open**: `http://localhost:5173`

## 🎨 Game Rules

- **Objective**: Be the first to play all your cards
- **Card Matching**: Match color or number/symbol
- **Special Cards**:
  - **Skip**: Next player loses their turn
  - **Reverse**: Change play direction
  - **Draw Two**: Next player draws 2 cards
  - **Wild**: Choose the next color
  - **Wild Draw Four**: Choose color + next player draws 4
- **UNO Rule**: Call "UNO" when you have one card left!

## 🎉 Production Features

✅ **Progressive Web App (PWA)**
✅ **Real-time Multiplayer**
✅ **Production Optimizations**
✅ **Complete UNO Game Logic**
✅ **Security Headers**
✅ **Asset Caching**
✅ **Environment Configuration**
✅ **TypeScript Support**

---

Built with ❤️ for the ultimate UNO experience!
