import React from 'react';
import { useGame } from '../context/GameContext';

const GameOverScreen: React.FC = () => {
    const { state, clientId, playAgain } = useGame();
    const { winner, players } = state;

    if (!winner) return null;
    
    const self = players.find(p => p.id === clientId);
    const isVictory = winner.id === clientId;
    const isHost = self?.isHost ?? false;

    return (
        <div className="min-h-screen flex flex-col items-center justify-center bg-zinc-900/90 backdrop-blur-sm p-4 text-white">
            <div className="text-center bg-zinc-800 p-8 rounded-2xl shadow-2xl border border-zinc-700">
                <h1 className="text-6xl font-bold mb-2">
                    {isVictory ? 'VICTORY!' : 'DEFEAT'}
                </h1>
                <p className="text-2xl text-yellow-400 mb-6">
                    <span className="font-bold">{winner.name}</span> won the game!
                </p>

                <div className="mb-8">
                    <h3 className="text-xl font-semibold mb-2">Final Standings</h3>
                    <ul className="space-y-2 text-left">
                        {players.sort((a,b) => a.hand.length - b.hand.length).map(p => (
                            <li key={p.id} className="flex justify-between p-2 bg-zinc-700 rounded-md">
                                <span>{p.name} {p.id === clientId && "(You)"}</span>
                                <span>{p.hand.length} cards left</span>
                            </li>
                        ))}
                    </ul>
                </div>
                
                {isHost && (
                     <button
                        onClick={playAgain}
                        className="w-full px-6 py-4 text-xl bg-red-600 text-white font-bold rounded-lg hover:bg-red-500 transition-colors"
                    >
                        Play Again
                    </button>
                )}
                 {!isHost && (
                    <p className="text-zinc-400">Waiting for host to start a new game...</p>
                 )}
            </div>
        </div>
    );
};

export default GameOverScreen;
