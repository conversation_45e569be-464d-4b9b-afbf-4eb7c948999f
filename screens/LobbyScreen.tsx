import React from 'react';
import { useGame } from '../context/GameContext';
import { HouseRules } from '../types';

const HouseRuleToggle: React.FC<{
    label: string;
    description: string;
    rule: keyof HouseRules;
    value: boolean;
    disabled: boolean;
    onToggle: (rule: keyof HouseRules, value: boolean) => void;
}> = ({ label, description, rule, value, disabled, onToggle }) => (
    <div className="flex items-center justify-between bg-zinc-700 p-3 rounded-lg">
        <div>
            <h4 className={`font-semibold ${disabled ? 'text-zinc-400' : 'text-white'}`}>{label}</h4>
            <p className="text-sm text-zinc-400">{description}</p>
        </div>
        <button
            onClick={() => onToggle(rule, !value)}
            disabled={disabled}
            className={`w-12 h-6 rounded-full p-1 transition-colors ${
                value ? 'bg-green-500' : 'bg-zinc-600'
            } ${disabled ? 'cursor-not-allowed' : ''}`}
        >
            <div
                className={`w-4 h-4 bg-white rounded-full shadow-md transform transition-transform ${
                    value ? 'translate-x-6' : 'translate-x-0'
                }`}
            />
        </button>
    </div>
);

const LobbyScreen: React.FC = () => {
    const { state, clientId, setHouseRule, startGame } = useGame();
    const { roomCode, players, houseRules } = state;
    const self = players.find(p => p.id === clientId);
    const isHost = self?.isHost ?? false;
    
    if (!self) return null;

    return (
        <div className="min-h-screen flex flex-col items-center justify-center bg-zinc-800 p-4">
            <div className="text-center mb-6">
                <h1 className="text-4xl font-bold text-white">Lobby</h1>
                <p className="text-zinc-400">Room Code</p>
                <div 
                    className="bg-zinc-900 text-3xl font-bold tracking-widest text-cyan-400 p-3 mt-2 rounded-lg inline-block border-2 border-zinc-700 cursor-pointer"
                    onClick={() => navigator.clipboard.writeText(roomCode || '')}
                    title="Copy to clipboard"
                >
                    {roomCode}
                </div>
            </div>

            <div className="w-full max-w-md bg-zinc-900 rounded-lg p-6 mb-6">
                <h2 className="text-2xl font-bold mb-4 border-b border-zinc-700 pb-2">Players ({players.length})</h2>
                <ul className="space-y-2">
                    {players.map(player => (
                        <li key={player.id} className="flex items-center justify-between bg-zinc-800 p-3 rounded-md">
                            <span className="font-semibold text-lg">{player.name}</span>
                            <div>
                                {player.isHost && <span className="text-xs font-bold bg-yellow-500 text-zinc-900 px-2 py-1 rounded-full mr-2">HOST</span>}
                                {player.id === clientId && <span className="text-xs font-bold bg-cyan-500 text-zinc-900 px-2 py-1 rounded-full">YOU</span>}
                            </div>
                        </li>
                    ))}
                </ul>
            </div>

            <div className="w-full max-w-md bg-zinc-900 rounded-lg p-6 mb-6">
                 <h2 className="text-2xl font-bold mb-4 border-b border-zinc-700 pb-2">House Rules</h2>
                 <div className="space-y-3">
                    <HouseRuleToggle label="Stacking" description="Allow stacking +2 and +4 cards." rule="stacking" value={houseRules.stacking} onToggle={setHouseRule} disabled={!isHost} />
                    <HouseRuleToggle label="Jump-In" description="Play an identical card out of turn." rule="jumpIn" value={houseRules.jumpIn} onToggle={setHouseRule} disabled={true} />
                    <HouseRuleToggle label="7-0 Rule" description="Swap hands on 7s, pass on 0s." rule="sevenZero" value={houseRules.sevenZero} onToggle={setHouseRule} disabled={true} />
                 </div>
            </div>
            
            {isHost && (
                <button
                    onClick={startGame}
                    disabled={players.length < 2}
                    className="w-full max-w-md px-6 py-4 text-xl bg-red-600 text-white font-bold rounded-lg hover:bg-red-500 transition-colors disabled:bg-zinc-600 disabled:cursor-not-allowed"
                >
                    Start Game
                </button>
            )}
        </div>
    );
};

export default LobbyScreen;
