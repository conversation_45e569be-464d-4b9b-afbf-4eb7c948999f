import React from 'react';
import { useGame } from '../context/GameContext';
import CardComponent from '../components/Card';
import ColorPickerModal from '../components/ColorPickerModal';
import { Card, CardColor, CardValue, Player } from '../types';

const PlayerDisplay: React.FC<{ player: Player; isCurrent: boolean }> = ({ player, isCurrent }) => (
    <div className={`flex flex-col items-center p-3 rounded-lg transition-all duration-300 ${isCurrent ? 'bg-yellow-500/20 ring-2 ring-yellow-400' : 'bg-zinc-800/50'}`}>
        <div className="relative">
            <CardComponent card={{ id: 'back' }} className="w-12 h-16" />
            <span className="absolute -top-2 -right-2 bg-red-600 text-white text-xs font-bold w-6 h-6 flex items-center justify-center rounded-full border-2 border-zinc-900">{player.hand.length}</span>
        </div>
        <p className="mt-2 font-semibold text-sm truncate">{player.name}</p>
        {player.saidUno && <p className="text-xs text-cyan-400 font-bold">UNO!</p>}
    </div>
);

const GameScreen: React.FC = () => {
    const { state, clientId, playCard, drawCard, setWildColor, sayUno } = useGame();
    const { players, discardPile, deck, currentPlayerIndex, isColorPickerOpen, playerPendingColorPick, message, drawTwoStack, drawFourStack } = state;

    const self = players.find(p => p.id === clientId);
    if (!self) return <div className="min-h-screen flex items-center justify-center bg-zinc-900 text-white">Waiting for game data...</div>;

    const opponents = players.filter(p => p.id !== clientId);
    const currentPlayer = players[currentPlayerIndex];
    const topCard = discardPile[0];
    const isMyTurn = currentPlayer?.id === clientId;
    
    const isCardPlayable = (card: Card): boolean => {
        if (!topCard || !isMyTurn) return false;

        // UI feedback for stacking rules
        if (drawFourStack > 0) {
            return card.value === CardValue.WILD_DRAW_FOUR;
        }
        if (drawTwoStack > 0) {
            return state.houseRules.stacking && card.value === CardValue.DRAW_TWO;
        }
        
        // Standard UI feedback
        return card.color === CardColor.BLACK || card.color === topCard.color || card.value === topCard.value;
    };

    const canSayUno = self.hand.length === 2 && !self.saidUno;

    return (
        <div className="w-full h-screen bg-zinc-900 flex flex-col overflow-hidden p-2 md:p-4">
            <ColorPickerModal 
                isOpen={isColorPickerOpen && playerPendingColorPick === clientId} 
                onSelectColor={setWildColor} 
            />

            {/* Opponents Area */}
            <div className="flex justify-center items-center gap-4 mb-4">
                {opponents.map(opp => (
                    <PlayerDisplay key={opp.id} player={opp} isCurrent={currentPlayer.id === opp.id} />
                ))}
            </div>

            {/* Game Piles */}
            <div className="flex-grow flex items-center justify-center gap-4 md:gap-8 relative">
                {message && (
                    <div className="absolute top-0 text-center bg-black/50 text-white font-bold p-3 rounded-lg animate-pulse z-30">
                        {message}
                    </div>
                )}
                <CardComponent 
                    card={{ id: 'back' }} 
                    onClick={isMyTurn ? drawCard : undefined} 
                    className={isMyTurn ? 'cursor-pointer ring-4 ring-cyan-400' : ''} 
                />
                {topCard && <CardComponent card={topCard} />}
            </div>

            {/* Human Player Area */}
            <div className="flex flex-col items-center">
                 {self.hand.length <= 2 && (
                    <button
                        onClick={sayUno}
                        disabled={!canSayUno}
                        className={`mb-4 px-8 py-3 rounded-full text-2xl font-bold transition-all ${
                            self.saidUno ? 'bg-green-600 text-white cursor-default' :
                            canSayUno ? 'bg-yellow-400 text-zinc-900 animate-bounce' : 'bg-zinc-700 text-zinc-500 cursor-not-allowed'
                        }`}
                    >
                        UNO!
                    </button>
                )}
                <div className={`w-full flex justify-center items-end h-48 p-2 rounded-t-xl transition-all duration-300 ${isMyTurn ? 'bg-yellow-500/10' : ''}`}>
                    <div className="flex space-x-[-3rem] md:space-x-[-4rem] justify-center">
                        {self.hand.map((card) => (
                           <div key={card.id} className="transition-transform duration-300 hover:-translate-y-4">
                                <CardComponent
                                    card={card}
                                    isPlayable={isCardPlayable(card)}
                                    onClick={() => isMyTurn && playCard(card.id)}
                                />
                           </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
};


export default GameScreen;