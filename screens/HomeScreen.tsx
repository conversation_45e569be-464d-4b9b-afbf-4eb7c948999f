import React, { useState } from 'react';
import { useGame } from '../context/GameContext';
import CardComponent from '../components/Card';
import { CardColor, CardValue } from '../types';

const HomeScreen: React.FC = () => {
  const { createRoom, joinRoom } = useGame();
  const [playerName, setPlayerName] = useState('');
  const [roomCode, setRoomCode] = useState('');
  const [showJoin, setShowJoin] = useState(false);

  const handleCreateRoom = () => {
    if (playerName.trim()) {
      createRoom(playerName.trim());
    }
  };

  const handleJoinRoom = () => {
    if (playerName.trim() && roomCode.trim()) {
      joinRoom(playerName.trim(), roomCode.trim());
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-zinc-900 p-4 text-white overflow-hidden">
      <div className="absolute top-0 left-0 w-full h-full opacity-10" style={{backgroundImage: 'radial-gradient(#444 1px, transparent 1px)', backgroundSize: '20px 20px'}}></div>
      
      <div className="z-10 text-center mb-8">
        <h1 className="text-6xl md:text-8xl font-bold text-red-600 tracking-tighter" style={{WebkitTextStroke: '2px white'}}>UNO</h1>
        <h2 className="text-4xl md:text-6xl font-bold">Club</h2>
        <p className="text-zinc-400 mt-2">The definitive PWA experience</p>
      </div>

      <div className="relative w-full max-w-sm flex items-center justify-center mb-10 h-40">
        <CardComponent card={{ id: 'c1', color: CardColor.RED, value: CardValue.SEVEN }} className="absolute -rotate-12 transform-gpu" />
        <CardComponent card={{ id: 'c2', color: CardColor.BLUE, value: CardValue.SKIP }} className="absolute z-10 scale-110" />
        <CardComponent card={{ id: 'c3', color: CardColor.YELLOW, value: CardValue.NINE }} className="absolute rotate-12 transform-gpu" />
      </div>

      <div className="z-10 w-full max-w-sm p-6 bg-zinc-800/80 backdrop-blur-sm rounded-2xl shadow-2xl border border-zinc-700">
        <input
          type="text"
          value={playerName}
          onChange={(e) => setPlayerName(e.target.value)}
          placeholder="Enter your nickname"
          className="w-full px-4 py-3 mb-4 bg-zinc-700 border border-zinc-600 rounded-lg text-white placeholder-zinc-400 focus:outline-none focus:ring-2 focus:ring-red-500"
        />
        
        {showJoin ? (
          <>
            <input
              type="text"
              value={roomCode}
              onChange={(e) => setRoomCode(e.target.value.toUpperCase())}
              placeholder="Enter Room Code"
              className="w-full px-4 py-3 mb-4 bg-zinc-700 border border-zinc-600 rounded-lg text-white placeholder-zinc-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              maxLength={5}
            />
            <button
              onClick={handleJoinRoom}
              disabled={!playerName.trim() || !roomCode.trim()}
              className="w-full px-4 py-3 bg-blue-600 text-white font-bold rounded-lg hover:bg-blue-500 transition-colors disabled:bg-zinc-600 disabled:cursor-not-allowed"
            >
              Join Room
            </button>
            <button onClick={() => setShowJoin(false)} className="w-full mt-2 text-zinc-400 hover:text-white">
              or Create a Room
            </button>
          </>
        ) : (
          <>
            <button
              onClick={handleCreateRoom}
              disabled={!playerName.trim()}
              className="w-full px-4 py-3 bg-red-600 text-white font-bold rounded-lg hover:bg-red-500 transition-colors disabled:bg-zinc-600 disabled:cursor-not-allowed"
            >
              Create Room
            </button>
             <button onClick={() => setShowJoin(true)} className="w-full mt-2 text-zinc-400 hover:text-white">
              or Join a Room
            </button>
          </>
        )}
      </div>
    </div>
  );
};

export default HomeScreen;
