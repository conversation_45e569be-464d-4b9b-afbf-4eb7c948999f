[build]
  publish = "dist"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"
  NPM_VERSION = "9"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[context.production.environment]
  VITE_BACKEND_URL = "https://swcf60xs-4000.inc1.devtunnels.ms"
  VITE_APP_NAME = "Sammy UNO Paradise"
  VITE_APP_VERSION = "1.0.0"

[context.deploy-preview.environment]
  VITE_BACKEND_URL = "https://swcf60xs-4000.inc1.devtunnels.ms"
  VITE_APP_NAME = "Sammy UNO Paradise (Preview)"
  VITE_APP_VERSION = "1.0.0-preview"

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' https://esm.sh https://cdn.socket.io; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; connect-src 'self' https://swcf60xs-4000.inc1.devtunnels.ms wss://swcf60xs-4000.inc1.devtunnels.ms; img-src 'self' data:;"

[[headers]]
  for = "/manifest.json"
  [headers.values]
    Content-Type = "application/manifest+json"

[[headers]]
  for = "/*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.png"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.svg"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
