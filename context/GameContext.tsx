import React, { createContext, useReducer, useContext, ReactNode, useEffect, useMemo } from 'react';
import { io, Socket } from 'socket.io-client';
import { GameState, GameAction, GamePhase, HouseRules, Card, CardColor } from '../types';
import { INITIAL_GAME_STATE } from '../constants';

const gameReducer = (state: GameState, action: GameAction): GameState => {
  switch (action.type) {
    case 'SET_GAME_STATE':
      return { ...state, ...action.payload };
    default:
      return state;
  }
};

interface GameContextProps {
    state: GameState;
    socket: Socket | null;
    clientId: string | null;
    createRoom: (playerName: string) => void;
    joinRoom: (playerName: string, roomCode: string) => void;
    setHouseRule: (rule: keyof HouseRules, value: boolean) => void;
    startGame: () => void;
    playCard: (cardId: string) => void;
    drawCard: () => void;
    setWildColor: (color: CardColor) => void;
    sayUno: () => void;
    playAgain: () => void;
}

const GameContext = createContext<GameContextProps | undefined>(undefined);

// Define your backend URL from environment variables
const BACKEND_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:4000';

export const GameProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [state, dispatch] = useReducer(gameReducer, INITIAL_GAME_STATE);
    const socket = useMemo(() => {
        console.log('🔌 Initializing socket connection to:', BACKEND_URL);
        const newSocket = io(BACKEND_URL, {
            transports: ['polling', 'websocket'], // Try polling first, then websocket
            timeout: 20000,
            autoConnect: true,
            upgrade: true,
            rememberUpgrade: false,
            reconnection: true,
            reconnectionAttempts: 5,
            reconnectionDelay: 1000
        });

        // Add immediate connection logging
        newSocket.on('connect', () => console.log('🔗 Socket connected'));
        newSocket.on('disconnect', () => console.log('🔌 Socket disconnected'));
        newSocket.on('connect_error', (err) => console.log('❌ Socket error:', err));

        return newSocket;
    }, []);

    useEffect(() => {
        socket.on('connect', () => {
            console.log('✅ Connected to server with id:', socket.id);
            // Small delay to ensure connection is stable
            setTimeout(() => {
                dispatch({ type: 'SET_GAME_STATE', payload: { gamePhase: GamePhase.HOME } });
            }, 100);
        });

        socket.on('connect_error', (error) => {
            console.error('❌ Connection error:', error);
            console.log('🔄 Retrying connection...');
            // Stay in connecting state to allow retry
        });

        socket.on('disconnect', (reason) => {
            console.log('❌ Disconnected from server. Reason:', reason);
            dispatch({ type: 'SET_GAME_STATE', payload: { ...INITIAL_GAME_STATE, gamePhase: GamePhase.CONNECTING } });
        });

        socket.on('game-state-update', (serverState: GameState) => {
            dispatch({ type: 'SET_GAME_STATE', payload: serverState });
        });
        
        socket.on('room-created', (roomCode: string) => {
            dispatch({ type: 'SET_GAME_STATE', payload: { roomCode }});
        });

        socket.on('error-message', (message: string) => {
            alert(message); // Simple error handling
        });

        return () => {
            socket.off('connect');
            socket.off('connect_error');
            socket.off('disconnect');
            socket.off('game-state-update');
            socket.off('room-created');
            socket.off('error-message');
            socket.disconnect();
        };
    }, [socket]);
    
    const createRoom = (playerName: string) => socket.emit('create-room', { playerName });
    const joinRoom = (playerName: string, roomCode: string) => socket.emit('join-room', { roomCode, playerName });
    const setHouseRule = (rule: keyof HouseRules, value: boolean) => socket.emit('set-house-rule', { roomCode: state.roomCode, rule, value });
    const startGame = () => socket.emit('start-game', { roomCode: state.roomCode });
    const playCard = (cardId: string) => socket.emit('play-card', { roomCode: state.roomCode, cardId });
    const drawCard = () => socket.emit('draw-card', { roomCode: state.roomCode });
    const setWildColor = (color: CardColor) => socket.emit('set-wild-color', { roomCode: state.roomCode, color });
    const sayUno = () => socket.emit('say-uno', { roomCode: state.roomCode });
    const playAgain = () => socket.emit('play-again', { roomCode: state.roomCode });
    
    return (
        <GameContext.Provider value={{
            state,
            socket,
            clientId: socket.id || null,
            createRoom,
            joinRoom,
            setHouseRule,
            startGame,
            playCard,
            drawCard,
            setWildColor,
            sayUno,
            playAgain
        }}>
            {children}
        </GameContext.Provider>
    );
};

export const useGame = () => {
    const context = useContext(GameContext);
    if (context === undefined) {
        throw new Error('useGame must be used within a GameProvider');
    }
    return context;
};
