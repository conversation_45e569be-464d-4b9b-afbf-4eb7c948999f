
import React from 'react';
import { CardColor } from '../types';

interface ColorPickerModalProps {
  isOpen: boolean;
  onSelectColor: (color: CardColor) => void;
}

const ColorPickerModal: React.FC<ColorPickerModalProps> = ({ isOpen, onSelectColor }) => {
  if (!isOpen) return null;

  const colors = [CardColor.RED, CardColor.YELLOW, CardColor.GREEN, CardColor.BLUE];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50">
      <div className="bg-zinc-800 p-6 rounded-lg shadow-xl text-center">
        <h2 className="text-2xl font-bold mb-4">Choose a Color</h2>
        <div className="flex justify-center space-x-4">
          {colors.map(color => (
            <button
              key={color}
              onClick={() => onSelectColor(color)}
              className={`w-20 h-20 rounded-full transition-transform transform hover:scale-110 focus:outline-none focus:ring-4 focus:ring-white
                ${
                  {
                    [CardColor.RED]: 'bg-red-600',
                    [CardColor.YELLOW]: 'bg-yellow-400',
                    [CardColor.GREEN]: 'bg-green-500',
                    [CardColor.BLUE]: 'bg-blue-500',
                  }[color]
                }
              `}
              aria-label={`Select ${color}`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default ColorPickerModal;
