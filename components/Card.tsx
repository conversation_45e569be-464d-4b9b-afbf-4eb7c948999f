import React from 'react';
import { Card, CardColor, CardValue } from '../types';
import { CARD_COLOR_MAP, CARD_TEXT_COLOR_MAP, SkipIcon, ReverseIcon, DrawTwoIcon, DrawFourIcon } from '../constants';

interface CardProps {
  card: Card | { id: 'back' };
  className?: string;
  onClick?: () => void;
  isPlayable?: boolean;
}

const renderValue = (card: Card) => {
  const iconProps = { className: 'w-1/2 h-1/2' };
  switch (card.value) {
    case CardValue.SKIP:
      return <SkipIcon {...iconProps} />;
    case CardValue.REVERSE:
      return <ReverseIcon {...iconProps} />;
    case CardValue.DRAW_TWO:
      return <DrawTwoIcon {...iconProps} />;
    case CardValue.WILD:
      return (
        <div className="w-full h-full grid grid-cols-2 grid-rows-2">
          <div className="bg-red-600 rounded-tl-full"></div>
          <div className="bg-yellow-400 rounded-tr-full"></div>
          <div className="bg-blue-500 rounded-bl-full"></div>
          <div className="bg-green-500 rounded-br-full"></div>
        </div>
      );
    case CardValue.WILD_DRAW_FOUR:
       return <DrawFourIcon {...iconProps} />;
    default:
      return <span className="text-5xl font-bold">{card.value}</span>;
  }
};

const CardComponent: React.FC<CardProps> = ({ card, className = '', onClick, isPlayable = false }) => {
  // Use the `in` operator for proper type narrowing.
  // If `color` exists, we know it's a `Card` object.
  if ('color' in card) {
    const bgColor = CARD_COLOR_MAP[card.color];
    const textColor = CARD_TEXT_COLOR_MAP[card.color];

    return (
      <div
        className={`
          w-20 h-28 md:w-24 md:h-36 
          rounded-lg p-2 flex flex-col justify-between 
          shadow-lg transition-all duration-300 ease-in-out select-none
          ${bgColor} ${textColor} ${className}
          ${onClick ? 'cursor-pointer' : ''}
          ${isPlayable ? 'ring-4 ring-cyan-400 ring-offset-2 ring-offset-zinc-900 transform -translate-y-2 hover:-translate-y-4' : 'hover:-translate-y-2'}
        `}
        onClick={onClick}
      >
        <div className="font-bold text-2xl">{card.value.length > 1 ? '' : card.value}</div>
        <div className="flex items-center justify-center flex-grow">
          {renderValue(card)}
        </div>
        <div className="font-bold text-2xl self-end transform rotate-180">{card.value.length > 1 ? '' : card.value}</div>
      </div>
    );
  }
  
  // Otherwise, it's the card back.
  return (
    <div className={`w-20 h-28 md:w-24 md:h-36 rounded-lg bg-zinc-800 border-2 border-zinc-600 flex items-center justify-center select-none ${className}`} onClick={onClick}>
      <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center">
          <h1 className="text-white font-bold text-2xl -rotate-12">UNO</h1>
      </div>
    </div>
  );
};

export default CardComponent;