# 🚀 Deploy Sammy UNO Paradise to Netlify

## Quick Deployment Steps

### Option 1: Drag & Drop Deployment (Fastest)

1. **Build is Ready**: The `dist` folder contains your production build
2. **Go to Netlify**: Visit [netlify.com](https://netlify.com) and log in
3. **Drag & Drop**: Simply drag the `dist` folder to the Netlify dashboard
4. **Site Name**: Change the site name to `sammy-uno-paradise`
5. **Done!** Your app will be live at `https://sammy-uno-paradise.netlify.app`

### Option 2: Git-based Deployment (Recommended)

1. **Push to GitHub**:
   ```bash
   git add .
   git commit -m "Production ready - Sammy UNO Paradise"
   git push origin main
   ```

2. **Connect to Netlify**:
   - Go to [netlify.com](https://netlify.com)
   - Click "New site from Git"
   - Connect your GitHub repository
   - Configure:
     - **Site name**: `sammy-uno-paradise`
     - **Build command**: `npm run build`
     - **Publish directory**: `dist`

3. **Environment Variables** (Set in Netlify dashboard):
   ```
   VITE_BACKEND_URL=https://swcf60xs-4000.inc1.devtunnels.ms
   VITE_APP_NAME=Sammy UNO Paradise
   VITE_APP_VERSION=1.0.0
   ```

4. **Deploy**: Click "Deploy site"

## Backend Configuration

Make sure your backend (running on the tunnel) has CORS configured to allow:
- `https://sammy-uno-paradise.netlify.app`
- `https://main--sammy-uno-paradise.netlify.app` (for deploy previews)

## What's Included

✅ **Production Build**: Optimized and minified
✅ **PWA Features**: Installable app with offline support
✅ **Environment Config**: Backend URL configured for your tunnel
✅ **Security Headers**: CSP and other security measures
✅ **Asset Caching**: Optimized loading performance
✅ **Responsive Design**: Works on all devices

## Testing After Deployment

1. Visit your deployed site
2. Test creating a room
3. Test joining with another device/browser
4. Verify real-time gameplay works
5. Test PWA installation on mobile

## Troubleshooting

- **Connection Issues**: Check if your backend tunnel is running
- **CORS Errors**: Ensure backend allows your Netlify domain
- **Build Errors**: Check environment variables are set correctly

Your app is ready to go live! 🎉
